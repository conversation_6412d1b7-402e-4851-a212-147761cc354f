from typing import Sequence

from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.application.exceptions import IncorrectOperationException
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.schemas.contact import Contact
from services.data_service.application.use_cases.contact.models.update_contact_input_boundary import (
    UpdateContactInputBoundary,
)


class UpdateContactUseCase:
    def __init__(
        self,
        contact_repository: ContactRepository,
        duplicate_check_service: DuplicateCheckService,
    ):
        self._contact_repository = contact_repository
        self._duplicate_check_service = duplicate_check_service

    async def execute_async(self, input_boundary: UpdateContactInputBoundary) -> Sequence[Contact]:
        contact_ids = [contact_input.id for contact_input in input_boundary.documents]
        existing_contacts = await self._contact_repository.search_by_id(ids=contact_ids)

        if len(existing_contacts) != len(contact_ids):
            raise IncorrectOperationException("Some contacts were not found")

        for contact in existing_contacts:
            if contact.rbac.owner_id != input_boundary.owner_id:
                raise IncorrectOperationException("Contact not owned by user")

        updated_contacts = []
        for contact_input in input_boundary.documents:
            existing_contact = next(c for c in existing_contacts if c.id == contact_input.id)

            updated_contact = Contact(
                id=existing_contact.id,
                type=existing_contact.type,
                last_name=contact_input.last_name,
                first_name=contact_input.first_name,
                nickname=contact_input.nickname,
                company=contact_input.company,
                street=contact_input.street,
                address=contact_input.address,
                city=contact_input.city,
                state=contact_input.state,
                zip=contact_input.zip,
                note=contact_input.note,
                birthday=contact_input.birthday,
                relationship=contact_input.relationship,
                rbac=existing_contact.rbac,
                tags=contact_input.tags,
                system_properties=existing_contact.system_properties,
                archived_at=existing_contact.archived_at,
            )
            updated_contacts.append(updated_contact)

        await self._duplicate_check_service.validate_no_document_duplicates(documents=updated_contacts)

        return await self._contact_repository.update(contacts=updated_contacts)
