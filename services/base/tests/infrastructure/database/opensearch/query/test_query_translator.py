from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.messages import InboxMessageStatus
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.air_quality import AirQuality, AirQualityFields
from services.base.domain.schemas.diary_events import (
    DiaryEventCustomDataFields,
    DiaryEvents,
    DiaryEventsFields,
    DiaryEventsPlanExtensionFields,
)
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressure
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.note import Note
from services.base.domain.schemas.events.symptom import Symptom
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.inbox.inbox_message import InboxMessage, InboxMessageFields
from services.base.domain.schemas.query.boolean_query import AndQuery, <PERSON><PERSON><PERSON><PERSON>, Or<PERSON>uery
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import (
    ExistsQuery,
    MatchType,
    PatternQuery,
    RadiusQuery,
    ValuesQuery,
)
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.query_operator import QueryOperator
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.templates.template import Template
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    AIR_QUALITY_INDEX,
    BODY_METRIC_INDEX,
    DIARY_EVENTS_INDEX,
    HEART_RATE_INDEX,
    INBOX_MESSAGE_INDEX,
    NOTE_INDEX,
    SYMPTOM_INDEX,
)
from services.base.infrastructure.database.opensearch.query_translator.query_translator import QueryTranslator


class TestQueryTranslator:

    def test_simple_values_query_translation_should_pass(self):
        values_query = ValuesQuery(field_name=DiaryEventsFields.NAME, values=["test"])
        type_query = TypeQuery(query=values_query, domain_types=[DiaryEvents])

        result = QueryTranslator.translate(Query(type_queries=[type_query]))

        expected_result = {"query": {"term": {f"{DiaryEventsFields.NAME}.keyword": "test"}}}

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_values_query_translation_with_optional_list_field_should_pass(self):
        optional_list_field = f"{DiaryEventsFields.CUSTOM_DATA}.{DiaryEventCustomDataFields.VALUE}"
        values = PrimitiveTypesGenerator.generate_n_random_strings(n=3)
        values_query = ValuesQuery(field_name=optional_list_field, values=values)
        type_query = TypeQuery(query=values_query, domain_types=[DiaryEvents])

        result = QueryTranslator.translate(query=Query(type_queries=[type_query]))

        expected_result = {"query": {"terms": {optional_list_field: values}}}

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_values_query_translation_with_optional_field_should_pass(self):
        optional_field_name = f"{DiaryEventsFields.PLAN_EXTENSION}.{DiaryEventsPlanExtensionFields.PLAN_ID}"
        values = PrimitiveTypesGenerator.generate_n_random_strings(n=5)
        values_query = ValuesQuery(field_name=optional_field_name, values=values)
        type_query = TypeQuery(query=values_query, domain_types=[DiaryEvents])

        result = QueryTranslator.translate(query=Query(type_queries=[type_query]))

        expected_result = {"query": {"terms": {optional_field_name: values}}}

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_simple_radius_query_translation_should_pass(self):
        geo_query = RadiusQuery(field_name=AirQualityFields.COORDINATES, radius="12km", latitude=40, longitude=-70)
        type_query = TypeQuery(query=geo_query, domain_types=[AirQuality])

        result = QueryTranslator.translate(Query(type_queries=[type_query]))

        expected_result = {
            "query": {
                "geo_distance": {
                    "distance": "12km",
                    AirQualityFields.COORDINATES: {
                        "lat": 40,
                        "lon": -70,
                    },
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [AIR_QUALITY_INDEX + "*"]

    def test_simple_multi_match_query_translation_should_pass(self):
        multi_match_query = PatternQuery(
            field_names=[DiaryEventsFields.NAME, DiaryEventsFields.EXPLANATION], pattern="test test"
        )
        type_query = TypeQuery(query=multi_match_query, domain_types=[DiaryEvents])

        result = QueryTranslator.translate(Query(type_queries=[type_query]))

        expected_result = {
            "query": {
                "multi_match": {
                    "query": "test test",
                    "fields": ["name", "explanation"],
                    "operator": "or",
                    "type": "cross_fields",
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_multi_match_with_fuzzy_query_translation_should_pass(self):
        multi_match_query = PatternQuery(
            field_names=[DiaryEventsFields.NAME, DiaryEventsFields.EXPLANATION],
            pattern="test test",
            match_type=MatchType.FUZZY,
        )
        type_query = TypeQuery(query=multi_match_query, domain_types=[DiaryEvents])

        result = QueryTranslator.translate(Query(type_queries=[type_query]))

        expected_result = {
            "query": {
                "multi_match": {
                    "query": "test test",
                    "fields": ["name", "explanation"],
                    "operator": "or",
                    "type": "best_fields",
                    "fuzziness": "AUTO",
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_values_query_wrapped_into_and_query_translation_should_pass(self):
        values_query = ValuesQuery(field_name=DiaryEventsFields.NAME, values=["test"])
        and_query = AndQuery(queries=[values_query])
        type_query = TypeQuery(query=and_query, domain_types=[DiaryEvents])

        result = QueryTranslator.translate(Query(type_queries=[type_query]))

        expected_result = {"query": {"bool": {"filter": [{"term": {f"{DiaryEventsFields.NAME}.keyword": "test"}}]}}}

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_values_query_with_or_query_wrapped_inside_and_query_translation_should_pass(self):
        """
        Tests that: ... where condition1 AND (condition2 OR condition3)
        """
        values_query1 = ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["test1"])
        values_query2 = ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["test2"])
        or_query = OrQuery(queries=[values_query1, values_query2])
        and_values_query = ValuesQuery(field_name=DiaryEventsFields.NAME, values=["ivo"])
        and_query = AndQuery(queries=[and_values_query, or_query])

        type_query = TypeQuery(query=and_query, domain_types=[DiaryEvents])

        result = QueryTranslator.translate(Query(type_queries=[type_query]))

        expected_result = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {f"{DiaryEventsFields.NAME}.keyword": "ivo"}},
                        {
                            "bool": {
                                "minimum_should_match": 1,
                                "should": [
                                    {"term": {DiaryEventsFields.TYPE: "test1"}},
                                    {"term": {DiaryEventsFields.TYPE: "test2"}},
                                ],
                            }
                        },
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_values_query_with_and_query_wrapped_inside_or_query_translation_should_pass(self):
        """
        Tests that: ... where condition1 OR (condition2 AND condition3)
        """
        values_query1 = ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["test1"])
        values_query2 = ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["test2"])
        and_query = AndQuery(queries=[values_query1, values_query2])
        or_values_query = ValuesQuery(field_name=DiaryEventsFields.NAME, values=["ivo"])
        or_query = OrQuery(queries=[or_values_query, and_query])

        type_query = TypeQuery(query=or_query, domain_types=[DiaryEvents])

        result = QueryTranslator.translate(Query(type_queries=[type_query]))

        expected_result = {
            "query": {
                "bool": {
                    "should": [
                        {"term": {f"{DiaryEventsFields.NAME}.keyword": "ivo"}},
                        {
                            "bool": {
                                "filter": [
                                    {"term": {DiaryEventsFields.TYPE: "test1"}},
                                    {"term": {DiaryEventsFields.TYPE: "test2"}},
                                ]
                            }
                        },
                    ],
                    "minimum_should_match": 1,
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_values_query_with_complex_and_query_wrapped_inside_or_query_translation_should_pass(self):
        """
        Tests that: ... where condition1 OR ((condition2 OR condition3) AND condition4)
        """
        values_query1 = ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["test1"])
        values_query2 = ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["test2"])
        or_sub_query = OrQuery(queries=[values_query1, values_query2])
        and_query = AndQuery(queries=[or_sub_query, ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["test3"])])
        or_values_query = ValuesQuery(field_name=DiaryEventsFields.NAME, values=["ivo"])
        or_query = OrQuery(queries=[or_values_query, and_query])

        type_query = TypeQuery(query=or_query, domain_types=[DiaryEvents])

        result = QueryTranslator.translate(Query(type_queries=[type_query]))

        expected_result = {
            "query": {
                "bool": {
                    "minimum_should_match": 1,
                    "should": [
                        {"term": {f"{DiaryEventsFields.NAME}.keyword": "ivo"}},
                        {
                            "bool": {
                                "filter": [
                                    {
                                        "bool": {
                                            "minimum_should_match": 1,
                                            "should": [
                                                {"term": {DiaryEventsFields.TYPE: "test1"}},
                                                {"term": {DiaryEventsFields.TYPE: "test2"}},
                                            ],
                                        }
                                    },
                                    {"term": {DiaryEventsFields.TYPE: "test3"}},
                                ]
                            }
                        },
                    ],
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_simple_two_datatypes_translation_should_pass(self):
        values_query = ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["test"])
        and_query = AndQuery(queries=[values_query])
        event_query = TypeQuery(query=and_query, domain_types=[DiaryEvents])

        note_values_query = ValuesQuery(field_name=DocumentLabels.TAGS, values=["test1"])
        note_query = TypeQuery(query=note_values_query, domain_types=[Note])

        result = QueryTranslator.translate(Query(type_queries=[event_query, note_query]))

        expected_result = {
            "query": {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "filter": [
                                    {"term": {DiaryEventsFields.TYPE: "test"}},
                                    {"term": {"_index": DIARY_EVENTS_INDEX + "*"}},
                                ]
                            }
                        },
                        {
                            "bool": {
                                "filter": [
                                    {"term": {f"{DocumentLabels.TAGS}.{DocumentLabels.TAG}.keyword": "test1"}},
                                    {"term": {"_index": NOTE_INDEX + "*"}},
                                ]
                            }
                        },
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*", NOTE_INDEX + "*"]

    def test_two_document_type_queries_with_or_query_should_pass(self):
        """
        Tests that if there is an 'OR' query it still creates filter occurrence type for index filtering
        """
        values_query = ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["test"])
        and_query = AndQuery(queries=[values_query])
        event_query = TypeQuery(query=and_query, domain_types=[DiaryEvents])

        note_values_query = ValuesQuery(field_name=DocumentLabels.TAGS, values=["test1"])
        note_or_query = OrQuery(queries=[note_values_query])
        note_query = TypeQuery(query=note_or_query, domain_types=[Note])

        result = QueryTranslator.translate(Query(type_queries=[event_query, note_query]))

        expected_result = {
            "query": {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "filter": [
                                    {"term": {DiaryEventsFields.TYPE: "test"}},
                                    {"term": {"_index": DIARY_EVENTS_INDEX + "*"}},
                                ]
                            }
                        },
                        {
                            "bool": {
                                "filter": [{"term": {"_index": NOTE_INDEX + "*"}}],
                                "should": [{"term": {f"{DocumentLabels.TAGS}.{DocumentLabels.TAG}.keyword": "test1"}}],
                                "minimum_should_match": 1,
                            }
                        },
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*", NOTE_INDEX + "*"]

    def test_all_document_types_without_query_should_pass(self):
        """
        Tests that if all type queries doesn't contain any query then None is return
        """
        empty_query = TypeQuery(domain_types=[DiaryEvents, Note], query=None)
        query = Query(type_queries=[empty_query])

        result = QueryTranslator.translate(query=query)

        assert result.query_as_dict is None
        assert result.indices == [DIARY_EVENTS_INDEX + "*", NOTE_INDEX + "*"]

    def test_one_document_type_with_empty_query_should_pass(self):
        """
        Tests that if one type query is empty it still creates bool for document type filtration
        """
        values_query = ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["test"])
        and_query = AndQuery(queries=[values_query])
        event_query = TypeQuery(query=and_query, domain_types=[DiaryEvents])

        empty_note_query = TypeQuery(query=None, domain_types=[Note])

        result = QueryTranslator.translate(Query(type_queries=[event_query, empty_note_query]))

        expected_result = {
            "query": {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "filter": [
                                    {"term": {DiaryEventsFields.TYPE: "test"}},
                                    {"term": {"_index": DIARY_EVENTS_INDEX + "*"}},
                                ]
                            }
                        },
                        {"bool": {"filter": [{"term": {"_index": NOTE_INDEX + "*"}}]}},
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*", NOTE_INDEX + "*"]

    def test_simple_multi_match_query_should_pass(self):
        match_query = PatternQuery(
            field_names=[DiaryEventsFields.EXPLANATION, DiaryEventsFields.NAME],
            pattern="test",
            operator=QueryOperator.OR,
        )
        query = Query(type_queries=[TypeQuery(domain_types=[DiaryEvents], query=match_query)])

        result = QueryTranslator.translate(query=query)

        expected_result = {
            "query": {
                "multi_match": {
                    "query": "test",
                    "fields": ["explanation", "name"],
                    "operator": "or",
                    "type": "cross_fields",
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_and_query_inside_and_query_should_be_merged_into_single_and_query(self):
        values_query1 = ValuesQuery(field_name=DiaryEventsFields.NAME, values=["honza"])
        nested_and_query = AndQuery(queries=[values_query1])
        root_values_query = ValuesQuery(field_name=DiaryEventsFields.NAME, values=["ivo"])
        root_and_query = AndQuery(queries=[root_values_query, nested_and_query])

        type_query = TypeQuery(domain_types=[DiaryEvents], query=root_and_query)
        query = Query(type_queries=[type_query])

        result = QueryTranslator.translate(query=query)

        expected_result = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {f"{DiaryEventsFields.NAME}.keyword": "ivo"}},
                        {"term": {f"{DiaryEventsFields.NAME}.keyword": "honza"}},
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [DIARY_EVENTS_INDEX + "*"]

    def test_must_not_query_with_or_operator_should_pass(self):
        status_values_query = ValuesQuery(
            field_name=InboxMessageFields.STATUS, values=[InboxMessageStatus.UNREAD.value]
        )
        title_values = PrimitiveTypesGenerator.generate_n_random_strings(n=2)
        title_values_query = ValuesQuery(field_name=InboxMessageFields.TITLE, values=title_values)
        not_query = NotQuery(operator=QueryOperator.OR, queries=[status_values_query, title_values_query])
        query = Query(type_queries=[TypeQuery(domain_types=[InboxMessage], query=not_query)])

        result = QueryTranslator.translate(query=query)

        expected_result = {
            "query": {
                "bool": {
                    "must_not": [
                        {"term": {InboxMessageFields.STATUS: InboxMessageStatus.UNREAD.value}},
                        {"terms": {f"{InboxMessageFields.TITLE}.keyword": title_values}},
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [INBOX_MESSAGE_INDEX + "*"]

    def test_must_not_query_with_and_operator_should_pass(self):
        status_values_query = ValuesQuery(
            field_name=InboxMessageFields.STATUS, values=[InboxMessageStatus.UNREAD.value]
        )
        title_values = PrimitiveTypesGenerator.generate_n_random_strings(n=2)
        title_values_query = ValuesQuery(field_name=InboxMessageFields.TITLE, values=title_values)
        not_query = NotQuery(operator=QueryOperator.AND, queries=[status_values_query, title_values_query])
        query = Query(type_queries=[TypeQuery(domain_types=[InboxMessage], query=not_query)])

        result = QueryTranslator.translate(query=query)

        expected_result = {
            "query": {
                "bool": {
                    "must_not": [
                        {
                            "bool": {
                                "filter": [
                                    {"term": {InboxMessageFields.STATUS: InboxMessageStatus.UNREAD.value}},
                                    {"terms": {f"{InboxMessageFields.TITLE}.keyword": title_values}},
                                ]
                            }
                        }
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [INBOX_MESSAGE_INDEX + "*"]

    def test_must_not_query_with_and_operator_and_match_query_should_pass(self):
        status_values_query = ValuesQuery(
            field_name=InboxMessageFields.STATUS, values=[InboxMessageStatus.UNREAD.value]
        )
        pattern = PrimitiveTypesGenerator.generate_random_string()
        title_match_query = PatternQuery(field_names=[InboxMessageFields.TITLE], pattern=pattern)
        not_query = NotQuery(operator=QueryOperator.AND, queries=[status_values_query, title_match_query])
        query = Query(type_queries=[TypeQuery(domain_types=[InboxMessage], query=not_query)])

        result = QueryTranslator.translate(query=query)

        expected_result = {
            "query": {
                "bool": {
                    "must_not": [
                        {
                            "bool": {
                                "must": [
                                    {"term": {InboxMessageFields.STATUS: InboxMessageStatus.UNREAD.value}},
                                    {
                                        "multi_match": {
                                            "query": pattern,
                                            "fields": [InboxMessageFields.TITLE],
                                            "type": "cross_fields",
                                            "operator": "or",
                                        }
                                    },
                                ]
                            }
                        }
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [INBOX_MESSAGE_INDEX + "*"]

    def test_nested_or_query_contains_minimum_should_match(self):
        status_values_query = ValuesQuery(
            field_name=InboxMessageFields.STATUS, values=[InboxMessageStatus.UNREAD.value]
        )
        pattern = PrimitiveTypesGenerator.generate_random_string()
        title_match_query = PatternQuery(field_names=[InboxMessageFields.TITLE], pattern=pattern)
        or_query = OrQuery(queries=[status_values_query])
        and_query = AndQuery(queries=[title_match_query, or_query])
        query = Query(type_queries=[TypeQuery(domain_types=[InboxMessage], query=and_query)])

        result = QueryTranslator.translate(query=query)

        expected_result = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "fields": ["title"],
                                "operator": "or",
                                "query": pattern,
                                "type": "cross_fields",
                            }
                        },
                        {
                            "bool": {
                                "minimum_should_match": 1,
                                "should": [{"term": {"status": InboxMessageStatus.UNREAD.value}}],
                            }
                        },
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [INBOX_MESSAGE_INDEX + "*"]

    def test_simple_or_query_contains_minimum_should_match(self):
        status_values_query = ValuesQuery(
            field_name=InboxMessageFields.STATUS, values=[InboxMessageStatus.UNREAD.value]
        )
        or_query = OrQuery(queries=[status_values_query])
        query = Query(type_queries=[TypeQuery(domain_types=[InboxMessage], query=or_query)])

        result = QueryTranslator.translate(query=query)

        expected_result = {
            "query": {
                "bool": {
                    "should": [{"term": {InboxMessageFields.STATUS: InboxMessageStatus.UNREAD.value}}],
                    "minimum_should_match": 1,
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [INBOX_MESSAGE_INDEX + "*"]

    def test_type_queries_does_not_contain_minimum_should_match(self):
        status_values_query = ValuesQuery(
            field_name=InboxMessageFields.STATUS, values=[InboxMessageStatus.UNREAD.value]
        )
        and_query = AndQuery(queries=[status_values_query])
        inbox_type_query = TypeQuery(domain_types=[InboxMessage], query=and_query)

        name_query = ValuesQuery(field_name=DiaryEventsFields.NAME, values=["test"])
        event_type_query = TypeQuery(query=name_query, domain_types=[DiaryEvents])

        result = QueryTranslator.translate(query=Query(type_queries=[inbox_type_query, event_type_query]))

        expected_result = {
            "query": {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "filter": [
                                    {"term": {"status": InboxMessageStatus.UNREAD.value}},
                                    {"term": {"_index": "inbox_message*"}},
                                ]
                            }
                        },
                        {
                            "bool": {
                                "filter": [
                                    {"term": {f"{DiaryEventsFields.NAME}.keyword": "test"}},
                                    {"term": {"_index": "diary_events*"}},
                                ]
                            }
                        },
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [INBOX_MESSAGE_INDEX + "*", DIARY_EVENTS_INDEX + "*"]

    def test_single_query_with_multiple_data_types_is_translated_correctly(self):
        leaf_query = CommonLeafQueries.organization_values_query(organizations=list(Organization))
        type_query = TypeQuery(domain_types=[Note, Symptom, HeartRate], query=leaf_query)
        query = Query(type_queries=[type_query])

        result = QueryTranslator.translate(query=query)

        expected_result = {
            "query": {
                "terms": {
                    "metadata.organization": [
                        "amazon",
                        "apple",
                        "facebook",
                        "fitbit",
                        "garmin",
                        "google",
                        "llif",
                        "netflix",
                        "oura",
                        "best_life",
                        "walmart",
                        "third_party",
                        "unknown",
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [NOTE_INDEX + "*", SYMPTOM_INDEX + "*", HEART_RATE_INDEX + "*"]

    def test_single_query_with_multiple_data_types_and_shared_index_types_included_is_translated_correctly(self):
        leaf_query = CommonLeafQueries.organization_values_query(organizations=list(Organization))
        type_query = TypeQuery(domain_types=[Note, BloodPressure, HeartRate], query=leaf_query)
        query = Query(type_queries=[type_query])

        result = QueryTranslator.translate(query=query)

        expected_result = {
            "query": {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "filter": [
                                    {"term": {"type": "blood_pressure"}},
                                    {
                                        "terms": {
                                            "metadata.organization": [
                                                "amazon",
                                                "apple",
                                                "facebook",
                                                "fitbit",
                                                "garmin",
                                                "google",
                                                "llif",
                                                "netflix",
                                                "oura",
                                                "best_life",
                                                "walmart",
                                                "third_party",
                                                "unknown",
                                            ]
                                        }
                                    },
                                    {"term": {"_index": "event_body_metric*"}},
                                ]
                            }
                        },
                        {
                            "bool": {
                                "filter": [
                                    {
                                        "terms": {
                                            "metadata.organization": [
                                                "amazon",
                                                "apple",
                                                "facebook",
                                                "fitbit",
                                                "garmin",
                                                "google",
                                                "llif",
                                                "netflix",
                                                "oura",
                                                "best_life",
                                                "walmart",
                                                "third_party",
                                                "unknown",
                                            ]
                                        }
                                    },
                                    {"term": {"_index": "event_note*"}},
                                ]
                            }
                        },
                        {
                            "bool": {
                                "filter": [
                                    {
                                        "terms": {
                                            "metadata.organization": [
                                                "amazon",
                                                "apple",
                                                "facebook",
                                                "fitbit",
                                                "garmin",
                                                "google",
                                                "llif",
                                                "netflix",
                                                "oura",
                                                "best_life",
                                                "walmart",
                                                "third_party",
                                                "unknown",
                                            ]
                                        }
                                    },
                                    {"term": {"_index": "heart_rate*"}},
                                ]
                            }
                        },
                    ]
                }
            }
        }

        assert result.query_as_dict == expected_result
        assert result.indices == [BODY_METRIC_INDEX + "*", NOTE_INDEX + "*", HEART_RATE_INDEX + "*"]

    def test_and_with_not_query_passes(self):
        e_q_1 = ExistsQuery(field_name=DiaryEventsFields.NAME)
        e_q_2 = ExistsQuery(field_name=DiaryEventsFields.NAME)
        not_q = NotQuery(queries=[e_q_2])

        type_query = TypeQuery(domain_types=[DiaryEvents], query=AndQuery(queries=[not_q, e_q_1]))
        query = Query(type_queries=[type_query])
        result = QueryTranslator.translate(query=query)

        expected = {
            "query": {
                "bool": {
                    "filter": [{"exists": {"field": "name"}}],
                    "must_not": [{"exists": {"field": "name"}}],
                }
            }
        }
        assert result.query_as_dict == expected

    def test_and_over_double_or_query_passes(self):
        or_q_1 = OrQuery(
            queries=[
                ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["exercise"]),
                ValuesQuery(field_name=DiaryEventsFields.TYPE, values=["symptom"]),
            ]
        )
        or_q_2 = OrQuery(
            queries=[
                ValuesQuery(field_name=DocumentLabels.TAGS, values=["water"]),
                ValuesQuery(field_name=DiaryEventsFields.NAME, values=["water"]),
            ]
        )
        type_query = TypeQuery(domain_types=[DiaryEvents], query=AndQuery(queries=[or_q_1, or_q_2]))
        query = Query(type_queries=[type_query])

        result = QueryTranslator.translate(query=query)
        expected = {
            "query": {
                "bool": {
                    "filter": [
                        {
                            "bool": {
                                "should": [
                                    {"term": {DiaryEventsFields.TYPE: "exercise"}},
                                    {"term": {DiaryEventsFields.TYPE: "symptom"}},
                                ],
                                "minimum_should_match": 1,
                            }
                        },
                        {
                            "bool": {
                                "should": [
                                    {"term": {"tags.tag.keyword": "water"}},
                                    {"term": {f"{DiaryEventsFields.NAME}.keyword": "water"}},
                                ],
                                "minimum_should_match": 1,
                            }
                        },
                    ]
                }
            }
        }
        assert result.query_as_dict == expected

    def test_query_over_abstract_types_passes(self):
        type_query = TypeQuery(domain_types=[Event, Template], query=None)
        query = Query(type_queries=[type_query])

        result = QueryTranslator.translate(query=query)
        expected_types = [
            "audio",
            "blood_glucose",
            "blood_pressure",
            "body_metric",
            "cardio",
            "content",
            "drink",
            "emotion",
            "event_template",
            "exercise",
            "food",
            "group_template",
            "image",
            "interactive",
            "medication",
            "event_group",
            "note",
            "sleep",
            "symptom",
            "person",
            "strength",
            "stress",
            "supplement",
            "text",
            "video",
        ]

        expected_indices = [
            "template*",
            "event_content*",
            "event_body_metric*",
            "event_nutrition*",
            "event_exercise*",
            "event_feeling*",
            "event_core*",
            "event_note*",
            "event_symptom*",
            "event_sleep*",
            "event_event_group*",
            "event_medication*",
            "event_person*",
        ]
        assert result.query_as_dict
        types = []
        for sub_query in result.query_as_dict["query"]["bool"]["should"]:
            sub_list = sub_query["bool"]["filter"]
            for sub_sub_query in sub_list:
                if sub_sub_query["term"].get("type"):
                    types.append(sub_sub_query["term"].get("type"))
                elif sub_sub_query["term"].get("_index"):
                    types.extend(sub_sub_query["term"].get("_index").split("_", 1)[1])

        assert set(types) == set(expected_types)
        assert set(result.indices) == set(expected_indices)
